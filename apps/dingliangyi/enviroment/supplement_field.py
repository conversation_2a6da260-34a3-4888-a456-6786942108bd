import csv
import time
from loguru import logger
import datetime
from resx.redis_types import Redis
from resx.config import CFG_REDIS_ZHUAN
from tqdm import tqdm

from word import EnvironmentalPenalties_LaunchWord


class DailyParseCounter:
    def __init__(self):
        self.redis = Redis(**CFG_REDIS_ZHUAN, db=0)
        self.daily_limit = 5500
        self.counter_key_prefix = "daily_parse_counter"
        self.progress_key = "csv_processing_progress"
        self.progress_bar = None

    def get_today_key(self):
        """获取今天的Redis键"""
        today = datetime.date.today().strftime("%Y-%m-%d")
        return f"{self.counter_key_prefix}:{today}"

    def get_today_count(self):
        """获取今天已调用的次数"""
        key = self.get_today_key()
        count = self.redis.get(key)
        return int(count) if count else 0

    def increment_count(self):
        """增加今天的调用次数"""
        key = self.get_today_key()
        count = self.redis.incr(key)
        # 设置过期时间为明天凌晨
        if count == 1:
            tomorrow = datetime.date.today() + datetime.timedelta(days=1)
            expire_time = int(datetime.datetime.combine(tomorrow, datetime.time.min).timestamp())
            current_time = int(datetime.datetime.now().timestamp())
            self.redis.expire(key, expire_time - current_time)
        return count

    def can_parse_today(self):
        """检查今天是否还能调用parse方法"""
        return self.get_today_count() < self.daily_limit

    def get_remaining_count(self):
        """获取今天剩余可调用次数"""
        return max(0, self.daily_limit - self.get_today_count())

    def save_progress(self, row_index, company_name):
        """保存处理进度"""
        progress_data = {
            'row_index': row_index,
            'company_name': company_name,
            'last_update': datetime.datetime.now().isoformat()
        }
        self.redis.hset(self.progress_key, 'current', str(progress_data))

    def get_progress(self):
        """获取处理进度"""
        progress_str = self.redis.hget(self.progress_key, 'current')
        if progress_str:
            try:
                import ast
                return ast.literal_eval(progress_str.decode('utf-8'))
            except:
                return None
        return None

    def init_progress_bar(self, total_companies, start_index=0):
        """初始化进度条"""
        if self.progress_bar:
            self.progress_bar.close()

        self.progress_bar = tqdm(
            total=total_companies,
            initial=start_index,
            desc="处理公司",
            unit="家",
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] 今日剩余:{postfix}",
            postfix=f"{self.get_remaining_count()}次"
        )

    def update_progress_bar(self, company_name="", parse_count=0):
        """更新进度条"""
        if self.progress_bar:
            remaining = self.get_remaining_count()
            self.progress_bar.set_postfix_str(f"{remaining}次 | {company_name} ({parse_count}条)")
            self.progress_bar.update(1)

    def close_progress_bar(self):
        """关闭进度条"""
        if self.progress_bar:
            self.progress_bar.close()
            self.progress_bar = None

    def wait_until_tomorrow(self):
        """等待到明天凌晨"""
        now = datetime.datetime.now()
        tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
        wait_seconds = (tomorrow - now).total_seconds()

        # 暂停主进度条并输出信息
        if self.progress_bar:
            self.progress_bar.write(f"今日调用次数已达上限，等待到明天凌晨继续处理...")
            self.progress_bar.write(f"预计等待时间: {wait_seconds / 3600:.1f} 小时")

        # 创建等待进度条
        wait_bar = tqdm(
            total=int(wait_seconds),
            desc="等待新的一天",
            unit="秒",
            bar_format="{l_bar}{bar}| {elapsed}<{remaining} {postfix}"
        )

        # 分段等待，每分钟更新一次进度条
        while datetime.datetime.now() < tomorrow:
            remaining = (tomorrow - datetime.datetime.now()).total_seconds()
            if remaining <= 0:
                break

            # 每次最多等待60秒，然后更新进度条
            sleep_time = min(60, remaining)
            time.sleep(sleep_time)

            elapsed = int(wait_seconds - remaining)
            wait_bar.n = elapsed
            wait_bar.set_postfix_str(f"剩余 {remaining / 3600:.1f} 小时")
            wait_bar.refresh()

        wait_bar.close()

        if self.progress_bar:
            self.progress_bar.write("新的一天开始，继续处理...")


def load_csv_data(file_path):
    """加载CSV数据到内存"""
    companies = []
    with open(file_path, newline='', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        for row_index, row in enumerate(reader):
            if row and len(row) > 0:
                companies.append((row_index, row[0]))
    return companies


def main():
    env = EnvironmentalPenalties_LaunchWord()
    counter = DailyParseCounter()
    file_path = r"C:\Users\<USER>\Downloads\env.csv"

    # 加载所有公司数据
    print("正在加载CSV文件...")
    companies = load_csv_data(file_path)
    total_companies = len(companies)
    print(f"共加载 {total_companies} 家公司")

    # 获取上次处理进度
    progress = counter.get_progress()
    start_index = 0
    if progress:
        start_index = progress['row_index'] + 1  # 从下一行开始
        print(f"从上次进度继续处理，起始位置: 第{start_index + 1}行，公司: {progress['company_name']}")
    else:
        print("从头开始处理")

    print(f"今日剩余可调用次数: {counter.get_remaining_count()}")

    # 初始化进度条
    counter.init_progress_bar(total_companies, start_index)

    processed_companies = 0
    total_parse_calls = 0

    # 主处理循环 - 持续运行直到所有公司处理完成
    try:
        while start_index < total_companies:
            # 检查今日是否还能调用
            if not counter.can_parse_today():
                counter.wait_until_tomorrow()
                continue

            row_index, name = companies[start_index]

            try:
                info = env.get_lvwang_api(name)
                if not info or 'data' not in info or 'data' not in info['data']:
                    counter.progress_bar.write(f"未获取到 {name} 的有效数据")
                    start_index += 1
                    counter.save_progress(row_index, name)
                    counter.update_progress_bar(name, 0)
                    continue

                company_parse_count = 0
                data_list = info['data']['data']

                # 处理当前公司的所有数据
                for data in data_list:
                    if not counter.can_parse_today():
                        counter.progress_bar.write(
                            f"处理 {name} 时达到今日调用上限，当前公司已处理 {company_parse_count}/{len(data_list)} 条数据")
                        # 保存当前进度（当前公司未完全处理完，所以不增加start_index）
                        counter.save_progress(row_index - 1, name)
                        break

                    env.parse(data, False)
                    counter.increment_count()
                    company_parse_count += 1
                    total_parse_calls += 1
                    time.sleep(1)
                else:
                    # 当前公司处理完成，移动到下一个公司
                    processed_companies += 1
                    start_index += 1
                    counter.save_progress(row_index, name)
                    counter.update_progress_bar(name, company_parse_count)

            except Exception as e:
                counter.progress_bar.write(f"处理公司 {name} 时发生错误: {e}")
                start_index += 1
                counter.save_progress(row_index, name)
                counter.update_progress_bar(name, 0)
                continue
    finally:
        # 确保进度条被正确关闭
        counter.close_progress_bar()

    print(f"🎉 所有公司处理完成！")
    print(f"总计处理了 {processed_companies} 家公司，{total_parse_calls} 次parse调用")

    # 清理进度记录
    counter.redis.hdel(counter.progress_key, 'current')
    print("已清理处理进度记录")


if __name__ == "__main__":
    main()
